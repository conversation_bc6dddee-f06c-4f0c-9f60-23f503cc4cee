import { describe, expect, it } from '@jest/globals';
import { JSONContent } from '@tiptap/core';

import {
    getPageId,
    htmlToJson,
    jsonToHtml,
    jsonToNode,
    jsonToText,
    tiptapExtensions,
} from '../../src/modules/editor/presentation/hocuspocus/collaboration.util';

describe('Collaboration Utils Tests', () => {
    describe('tiptapExtensions', () => {
        it('должен экспортировать массив extensions', () => {
            expect(Array.isArray(tiptapExtensions)).toBe(true);
            expect(tiptapExtensions.length).toBeGreaterThan(0);
        });

        it('должен содержать StarterKit extension', () => {
            // StarterKit.configure() должен вернуть объект (мок)
            expect(tiptapExtensions[0]).toBeDefined();
        });
    });

    describe('jsonToHtml', () => {
        it('должен конвертировать JSON в HTML', () => {
            const tiptapJson: JSONContent = {
                type: 'doc',
                content: [
                    {
                        type: 'paragraph',
                        content: [
                            {
                                type: 'text',
                                text: 'Hello World',
                            },
                        ],
                    },
                ],
            };

            const html = jsonToHtml(tiptapJson);
            expect(html).toBe('<p>Hello World</p>');
        });
    });

    describe('htmlToJson', () => {
        it('должен конвертировать HTML в JSON', () => {
            const html = '<p>Hello World</p>';
            const json = htmlToJson(html);

            expect(json).toEqual({
                type: 'doc',
                content: [
                    { type: 'paragraph', attrs: { textAlign: null }, content: [{ type: 'text', text: 'Hello World' }] },
                ],
            });
        });
    });

    describe('jsonToText', () => {
        it('должен конвертировать JSON в текст', () => {
            const tiptapJson: JSONContent = {
                type: 'doc',
                content: [
                    {
                        type: 'paragraph',
                        content: [
                            {
                                type: 'text',
                                text: 'Hello World',
                            },
                        ],
                    },
                ],
            };

            const text = jsonToText(tiptapJson);
            expect(text).toBe('Hello World');
        });
    });

    describe('jsonToNode', () => {
        it('должен конвертировать JSON в Node', () => {
            const tiptapJson: JSONContent = {
                type: 'doc',
                content: [],
            };

            const node = jsonToNode(tiptapJson);
            expect(node).toBeDefined();
            expect(typeof node).toBe('object');
        });
    });

    describe('getPageId', () => {
        it('должен вернуть pageId из documentName', () => {
            const documentName = 'test-page-id-123';
            const pageId = getPageId(documentName);
            expect(pageId).toBe(documentName);
        });

        it('должен вернуть undefined для пустого documentName', () => {
            expect(getPageId('')).toBeUndefined();
            expect(getPageId(null as any)).toBeUndefined();
            expect(getPageId(undefined as any)).toBeUndefined();
        });
    });
});
