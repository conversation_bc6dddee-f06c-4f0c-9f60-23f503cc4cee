import { Injectable } from '@nestjs/common';
import { GrpcSubscribe, ServiceRequest, ServiceResponse } from '@skillspace/grpc';

import { PageService } from '../application/services/page.service';

@Injectable()
export class PageManagement {
    constructor(private readonly pageService: PageService) {}

    @GrpcSubscribe('EditorService', 'CreateManyPages', 'v1')
    public async createManyPages(
        data: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'>,
    ): Promise<ServiceResponse<'EditorService', 'CreateManyPages', 'v1'>> {
        const pages = await this.pageService.createManyPages(data.pages);
        return { pages };
    }

    @GrpcSubscribe('EditorService', 'RemoveManyPages', 'v1')
    public async RemoveManyPages(
        data: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'>,
    ): Promise<ServiceResponse<'EditorService', 'RemoveManyPages', 'v1'>> {
        return await this.pageService.removeManyPages(data.pageIds);
    }
}
