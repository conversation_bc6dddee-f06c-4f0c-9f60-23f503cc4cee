import { Inject, Injectable } from '@nestjs/common';
import { eq, inArray } from 'drizzle-orm';

import { DRIZZLE_DB } from '../../../../drizzle/drizzle.module';
import { DrizzleDB, DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { dbOrTx } from '../../../../drizzle/drizzle.utils';
import { InsertablePage, Page, pages, UpdatablePage } from '../../../../drizzle/schema';
import { IPageRepo } from '../../application/repositories/page-repository.interface';

@Injectable()
export class PageRepo implements IPageRepo {
    constructor(@Inject(DRIZZLE_DB) private readonly db: DrizzleDB) {}

    async findById(pageId: string, trx?: DrizzleTransaction): Promise<Page | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.select().from(pages).where(eq(pages.id, pageId)).limit(1);
        return result[0];
    }

    async updatePage(updatablePage: UpdatablePage, pageId: string, trx?: DrizzleTransaction): Promise<void> {
        return this.updatePages(updatablePage, [pageId], trx);
    }

    async updatePages(updatePageData: UpdatablePage, pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (pageIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);

        const now = new Date();

        await db
            .update(pages)
            .set({ ...updatePageData, updatedAt: now })
            .where(inArray(pages.id, pageIds))
            .execute();
    }

    async insertPage(insertablePage: InsertablePage, trx?: DrizzleTransaction): Promise<Page | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.insert(pages).values(insertablePage).returning().execute();
        return result?.[0];
    }

    async insertManyPages(insertablePages: InsertablePage[], trx?: DrizzleTransaction): Promise<Page[]> {
        if (insertablePages.length === 0) {
            return [];
        }

        const db = dbOrTx(this.db, trx);
        const result = await db.insert(pages).values(insertablePages).returning().execute();
        return result;
    }

    async deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);
        await db.delete(pages).where(eq(pages.id, pageId)).execute();
    }

    async deleteManyPages(pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (pageIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);
        await db.delete(pages).where(inArray(pages.id, pageIds)).execute();
    }

    async exists(pageId: string): Promise<boolean> {
        const result = await this.db
            .select({ id: pages.id })
            .from(pages)
            .where(eq(pages.id, pageId))
            .limit(1)
            .execute();

        return result.length > 0;
    }
}
