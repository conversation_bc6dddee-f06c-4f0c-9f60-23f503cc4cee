import { DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { InsertablePage, Page, UpdatablePage } from '../../../../drizzle/schema';

// Интерфейсы для массовых операций со страницами
export interface CreatePageRequest {
    serviceId: string;
    entityId: string;
    schoolId: string;
}

export interface CreateManyPagesRequest {
    pages: CreatePageRequest[];
}

export interface CreatePageResponse extends CreatePageRequest {
    pageId: string;
}

export interface RemoveManyPagesRequest {
    pageIds: string[];
}

export interface RemoveManyPagesResponse {
    success: boolean;
    removedPageIds: string[];
}

export interface IPageRepo {
    /**
     * Найти страницу по ID
     */
    findById(pageId: string, trx?: DrizzleTransaction): Promise<Page | undefined>;

    /**
     * Обновить одну страницу
     */
    updatePage(updatablePage: UpdatablePage, pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Обновить несколько страниц
     */
    updatePages(updatePageData: UpdatablePage, pageIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Вставить новую страницу
     */
    insertPage(insertablePage: InsertablePage, trx?: DrizzleTransaction): Promise<Page | undefined>;

    /**
     * Вставить несколько страниц
     */
    insertManyPages(insertablePages: InsertablePage[], trx?: DrizzleTransaction): Promise<Page[]>;

    /**
     * Удалить страницу по ID
     */
    deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Удалить несколько страниц по ID
     */
    deleteManyPages(pageIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Проверить наличие страницы в БД
     */
    exists(pageId: string): Promise<boolean>;
}
